import config from '.';
import { DataSource, DataSourceOptions } from 'typeorm';
import { User } from 'src/crypto/entities/user.entity';
import { Currency } from '../crypto/entities/currency.entity';
import { Network } from '../crypto/entities/network.entity';
import { Wallet } from '../crypto/entities/wallet.entity';
import { Market } from '../crypto/entities/markets.entity';
import { Trade } from '../crypto/entities/trades.entity';
import { Orders } from '../crypto/entities/orders.entity';
import { Transaction } from '../crypto/entities/transactions.entity';
import { SwapTransaction } from '../crypto/entities/swap-transaction.entity';
import { SwapQuotation } from '../crypto/entities/swap-quotation.entity';
import { Deposit } from '../crypto/entities/deposits.entity';
import { Address } from '../crypto/entities/address.entity';

// eslint-disable-next-line @typescript-eslint/no-require-imports
require('dotenv').config();

export const typeOrmConfig: DataSourceOptions = {
  type: 'mysql',
  url: config.db.url,
  // autoLoadEntities:true,
  migrations: ['dist/db/migrations/*.js'],
  ssl: false,
  entities: [
    User,
    Currency,
    Network,
    Wallet,
    Market,
    Trade,
    Orders,
    Transaction,
    SwapTransaction,
    SwapQuotation,
    Deposit,
    Address,
  ],
  subscribers: [],
  logging: false,
  synchronize: true,
  // connectTimeout:30000
};

export const dataSource = new DataSource(typeOrmConfig);
