import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Currency } from '../entities/currency.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class CurrencyRepository extends TypeOrmRepository<Currency> {
  constructor(private readonly dataSource: DataSource) {
    super(Currency, dataSource.createEntityManager());
  }

  async findAllCurrencies(name?: string): Promise<Currency[]> {
    // Optional parameter
    const query = this.createQueryBuilder('currencies').leftJoinAndSelect(
      'currencies.networks',
      'networks',
    );

    if (name) {
      query.where('currencies.name LIKE :name', {
        name: `%${name}%`,
      });
    }

    return await query.getMany();
  }
}
