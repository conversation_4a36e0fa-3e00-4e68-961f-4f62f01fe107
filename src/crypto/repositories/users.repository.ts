import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { User } from '../entities/user.entity';
import { CreateUserDto } from '../dtos/users.dto';

@Injectable()
export class UserRepository extends TypeOrmRepository<User> {
  constructor(private readonly dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  async createUser(users: CreateUserDto): Promise<User> {
    const user = await this.save({
      id: users.id,
      userId: users.userId,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      sn: users.sn,
      reference: users.reference,
      displayName: users.displayName,
    });

    return user;
  }

  async getUserByUserId(userId: string): Promise<User> {
    return await this.findOne({
      where: { userId: userId },
    });
  }

  async getUserById(id: string): Promise<User> {
    return await this.findOne({
      where: { userId: id },
    });
  }
}
