import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { OrderRepository } from '../repositories/orders.repository';
import { QuidaxService } from '@app/quidax';
import { CreateOrderDto } from '../dtos/orders.dto';
import { randomUUID } from 'crypto';
import { AuthData } from '@crednet/authmanager';
import { UserRepository } from '../repositories/users.repository';
import { Orders, OrderStatus } from '../entities/orders.entity';
import { TradeRepository } from '../repositories/trades.repository';

@Injectable()
export class OrdersService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
    private readonly tradeRepository: TradeRepository,
  ) {}

  async createOrder(auth: AuthData, createOrderDto: CreateOrderDto) {
    const isKycVerified = true;
    if (!isKycVerified) {
      throw new BadRequestException('KYC not verified');
    }
    const reference = 'cp_ord_' + randomUUID();

    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    let response: any;

    if (createOrderDto.order_type === 'limit') {
      response = await this.quidaxService.createOrder(user.id, {
        market: createOrderDto.baseCurrency + createOrderDto.quoteCurrency,
        side: createOrderDto.side,
        price: createOrderDto.price,
        volume: createOrderDto.volume,
        ord_type: createOrderDto.order_type,
        reference,
      });
    } else {
      response = await this.quidaxService.createOrder(user.id, {
        market: createOrderDto.baseCurrency + createOrderDto.quoteCurrency,
        side: createOrderDto.side,
        volume: createOrderDto.volume,
        ord_type: createOrderDto.order_type,
        reference,
      });
    }

    if (response.status === 'error') {
      throw new BadRequestException(response.message);
    }

    const order = await this.orderRepository.createOrder({
      id: response.data.id,
      reference: response.data.reference || reference,
      market_id: response.data.market.id,
      base_unit: response.data.market.base_unit,
      quote_unit: response.data.market.quote_unit,
      side: response.data.side,
      order_type: response.data.order_type,
      price_unit: response.data.price.unit,
      price_amount: response.data.price.amount,
      avg_price_unit: response.data.avg_price.unit,
      avg_price_amount: response.data.avg_price.amount,
      volume_unit: response.data.volume.unit,
      volume_amount: response.data.volume.amount,
      origin_volume_unit: response.data.origin_volume.unit,
      origin_volume_amount: response.data.origin_volume.amount,
      executed_volume_unit: response.data.executed_volume.unit,
      executed_volume_amount: response.data.executed_volume.amount,
      status: response.data.status,
      trades_count: response.data.trades_count,
      userId: user.id,
    });

    return order;
  }

  async cancelOrder(auth: AuthData, orderId: string) {
    const order = await this.orderRepository.getOrder(orderId);
    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    const response = await this.quidaxService.cancelOrder(user.id, orderId);

    if (response.status === 'error') {
      throw new BadRequestException(response.message);
    }

    const updatedOrder = await this.orderRepository.update(
      {
        id: orderId,
      },
      {
        status: response.data.status,
      },
    );

    return updatedOrder;
  }

  async getOrder(orderId: string): Promise<Orders> {
    const order = await this.orderRepository.getOrder(orderId);
    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }
    return order;
  }

  async getOrdersByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ) {
    const user = await this.userRepository.getUserByUserId(userId);

    const { orders, total } = await this.orderRepository.getOrdersByUserId(
      user.id,
      startDate,
      endDate,
      page,
      limit,
    );
    return { orders, total, page, limit };
  }

  async updateOrderByReference(reference: string, data: any): Promise<Orders> {
    const order = await this.orderRepository.findOne({
      where: { reference },
    });
    order.trades_count = data.trades_count;
    order.status = data.status;

    return await this.orderRepository.save(order);
  }

  async verifyOrderStatus(orderId: string): Promise<any> {
    const order = await this.orderRepository.getOrder(orderId);

    const fetchOrder = await this.quidaxService.fetchOrderDetails(
      order.user.id,
      orderId,
    );

    const orderStatus = fetchOrder.status;

    this.updateOrderByReference(order.reference, fetchOrder);

    // todo add notifications for each status change case

    if (orderStatus === 'done' && fetchOrder.trades_count > 0) {
      // Handle order completed event
      for (const trade of fetchOrder.trades) {
        await this.tradeRepository.save({
          id: trade.id,
          market_id: trade.market.id,
          market_base_unit: trade.market.base_unit,
          market_quote_unit: trade.market.quote_unit,
          price_amount: trade.price.amount,
          price_unit: trade.price.unit,
          volume_amount: trade.volume.amount,
          volume_unit: trade.volume.unit,
          total_amount: trade.total.amount,
          total_unit: trade.total.unit,
          order,
        });
      }
    }
  }

  async requeryOrderStatus(pageNumber: number) {
    console.log('running job:: verifyOrderStatus ', pageNumber);

    const items = await this.orderRepository.findMany(
      {
        page: pageNumber,
        limit: 10,
      },
      {
        where: {
          status: OrderStatus.WAIT,
        },
      },
    );

    for (const item of items.items) {
      await this.verifyOrderStatus(item.id);
    }

    if (pageNumber < items.meta.totalPages) {
      return this.requeryOrderStatus(++pageNumber);
    }
  }
}
