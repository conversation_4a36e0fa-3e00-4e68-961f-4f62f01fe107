import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('users')
export class User extends BaseEntity {
  @Column({ nullable: false })
  userId: string;

  @Column({ nullable: false })
  firstName: string;

  @Column({ nullable: false })
  lastName: string;

  @Column({ nullable: false })
  sn: string;

  @Column({ nullable: false })
  email: string;

  @Column({ nullable: true })
  reference: string;

  @Column({ nullable: true })
  displayName: string;
}
