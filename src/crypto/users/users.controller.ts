import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { AuthData, JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CreatePaymentAddressDto, ValidateAddressDto } from '../dtos/users.dto';

@Controller('users')
@ApiBearerAuth('JWT')
@ApiTags('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('/')
  async getOrCreateUser(@GetAuthData() auth: AuthData) {
    return this.usersService.getOrCreateUser(auth);
  }

  @Post('/address')
  async createPaymentAddress(
    @GetAuthData() auth: AuthData,
    @Body() payload: CreatePaymentAddressDto,
  ) {
    console.log(85858);

    return this.usersService.createPaymentAddress(
      auth.id.toString(),
      payload.currency,
      payload.network,
    );
  }

  @Get('/addresses/:currency')
  async getPaymentAddresses(
    @GetAuthData() auth: AuthData,
    @Param('currency') curr: string,
  ) {
    return this.usersService.getPaymentAddressesForACryptoCurrency(auth, curr);
  }

  @Post('/validate-address')
  async validateAddress(@Body() payload: ValidateAddressDto) {
    return this.usersService.validateAddress(payload.currency, payload.address);
  }
}
