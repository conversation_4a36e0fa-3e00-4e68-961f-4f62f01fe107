import { QuidaxService } from '@app/quidax';
import { BadRequestException, Injectable } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { AuthData } from '@crednet/authmanager';
import { User } from '../entities/user.entity';
import { Address } from '@app/quidax/quidax.interface';
import { WalletRepository } from '../repositories/wallet.repository';
import { AddressRepository } from '../repositories/address.repository';
import { AddressStatus } from '../entities/address.entity';
import { log } from 'console';

@Injectable()
export class UsersService {
  constructor(
    private readonly usersRepo: UserRepository,
    private readonly quidaxService: QuidaxService,
    private readonly walletRepository: WalletRepository,
    private readonly addressRepository: AddressRepository,
  ) {}

  async getOrCreateUser(auth: AuthData): Promise<User> {
    try {
      const user = await this.usersRepo.getUserByUserId(auth.id.toString());
      if (!user) {
        const crednetEmail =
          auth.email.split('@')[0] +
          '-crednet' +
          '@' +
          auth.email.split('@')[1];
        const payload = {
          email: crednetEmail,
          first_name: auth.name,
          last_name: auth.last_name,
        };

        console.log(payload);

        const result = await this.quidaxService.createSubAccount(payload);

        const walletNgn = await this.quidaxService.fetchWallet(
          result.id,
          'ngn',
        );

        const walletUsdt = await this.quidaxService.fetchWallet(
          result.id,
          'usd',
        );

        const user = await this.usersRepo.createUser({
          id: result.id,
          userId: auth.id.toString(),
          email: result.email,
          firstName: result.first_name,
          lastName: result.last_name,
          sn: result.sn,
          reference: result.reference,
          displayName: result.display_name,
        });

        await this.walletRepository.createWalletDefault(
          {
            id: walletNgn.id,
            currency: walletNgn.currency,
            balance: walletNgn.balance,
            depositAddress: walletNgn.deposit_address,
            convertedBalance: walletNgn.converted_balance,
            locked: walletNgn.locked,
            staked: walletNgn.staked,
            destinationTag: walletNgn.destination_tag,
          },
          user.userId,
        );

        await this.walletRepository.createWalletDefault(
          {
            id: walletUsdt.id,
            currency: walletUsdt.currency,
            balance: walletUsdt.balance,
            depositAddress: walletUsdt.deposit_address,
            convertedBalance: walletUsdt.converted_balance,
            locked: walletUsdt.locked,
            staked: walletUsdt.staked,
            destinationTag: walletUsdt.destination_tag,
          },
          user.userId,
        );

        return user;
      }
      return user;
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Error creating User: ' + error);
    }
  }

  async createPaymentAddress(
    id: string,
    currency: string,
    network?: string,
  ): Promise<Address> {
    const user = await this.usersRepo.getUserById(id);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const address =
      await this.quidaxService.createPaymentAddrressForACryptoCurrency(
        user.id,
        currency,
        network,
      );
    
    log(address)

    await this.addressRepository.save({
      id: address.id,
      currency: address.currency,
      address: address.address,
      destination_tag: address.destination_tag,
      total_payments: address.total_payments,
      user,
      network: address.network,
    });

    return address;
  }

  async verifyPaymentAddressGeneration(addressId: string): Promise<void> {
    log('initititin')
    const fetchAdress = await this.addressRepository.findOne({
      where: { id: addressId },
      relations: ['user'],
    });

    if (!fetchAdress) {
      throw new BadRequestException('Address not found');
    }

    const userId = fetchAdress.user.id;
    const currency = fetchAdress.currency;

    const address = await this.quidaxService.fetchWalletAddressById(
      addressId,
      userId,
      currency,
    );

    if (address.address !== null) {
      await this.addressRepository.update(
        { id: address.id },
        {
          status: AddressStatus.SUCCESS,
          address: address.address,
          destination_tag: address.destination_tag,
        },
      );

      const getWallet = await this.walletRepository.getUserWalletByCurrency(
        userId,
        currency,
      );
      if (getWallet) return;

      const result = await this.quidaxService.fetchWallet(userId, currency);

      await this.walletRepository.createWallet(
        {
          id: result.id,
          currency: result.currency,
          balance: result.balance,
          depositAddress: result.deposit_address,
          convertedBalance: result.converted_balance,
          locked: result.locked,
          staked: result.staked,
          destinationTag: result.destination_tag,
        },
        fetchAdress.user.id,
      );
    }
  }

  async requeryPaymentAddressGeneration(pageNumber: number) {
    console.log('running job:: verifyPaymentAddressGeneration ', pageNumber);
    const items = await this.addressRepository.findMany(
      {
        page: pageNumber,
        limit: 10,
      },
      {
        where: {
          status: AddressStatus.PROCESSING,
        },
      },
    );

    for (const item of items.items) {
      await this.verifyPaymentAddressGeneration(item.id);
    }

    if (pageNumber < items.meta.totalPages) {
      return this.requeryPaymentAddressGeneration(++pageNumber);
    }
  }

  async getPaymentAddressesForACryptoCurrency(
    auth: AuthData,
    currency: string,
  ): Promise<Address[]> {
    const user = await this.usersRepo.getUserById(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const addresses = await this.quidaxService.fetchWalletPaymentAddresses(
      user.id,
      currency,
    );

    return addresses;
  }

  async validateAddress(currency: string, address: string): Promise<any> {
    const result = await this.quidaxService.validateAddress(currency, address);
    return result;
  }
}
