import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UsersService } from './users.service';

@Injectable()
export class UsersCron {
  constructor(private readonly usersService: UsersService) {}

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async requeryPaymentAddressGeneration() {
    await this.usersService.requeryPaymentAddressGeneration(1);
  }
}
