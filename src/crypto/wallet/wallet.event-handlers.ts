import { Injectable, Logger } from '@nestjs/common';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { WalletService } from './wallet.service';

/**
 * Handler for wallet creation events
 */
@Injectable()
export class WalletCreatedEventHandler implements WebhookEventHandler {
  private readonly logger = new Logger(WalletCreatedEventHandler.name);

  constructor(private readonly walletService: WalletService) {}

  async handle(data: any): Promise<void> {
    console.log(8889989999999999);

    const { id, user, currency, address, destination_tag } = data;
    await this.walletService.createWallet(
      id,
      user.id,
      currency,
      address,
      destination_tag,
    );

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(
      `Wallet created for user ${user.id} with currency ${currency}`,
    );
  }
}

/**
 * Handler for wallet update events
 */
@Injectable()
export class WalletUpdatedEventHandler implements WebhookEventHandler {
  private readonly logger = new Logger(WalletUpdatedEventHandler.name);

  constructor(private readonly walletService: WalletService) {}

  async handle(data: any): Promise<void> {
    const wallet = await this.walletService.handleWalletUpdatedEvent(data);

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(
      `Wallet updated for user ${wallet.user.id} with currency ${wallet.currency.currencyCode}`,
    );
  }
}
