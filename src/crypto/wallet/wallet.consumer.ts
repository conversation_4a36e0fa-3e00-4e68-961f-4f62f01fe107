import { Injectable } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Events } from '../../utils/queue';
import { BaseWebhookConsumer } from '../../utils/webhook/base-webhook.consumer';
import { WebhookEventHandlerFactory } from '../../utils/webhook/webhook-event.handler';
import {
  WalletCreatedEventHandler,
  WalletUpdatedEventHandler,
} from './wallet.event-handlers';

@Injectable()
export class WalletConsumer extends BaseWebhookConsumer {
  constructor(
    protected readonly rmqService: RabbitmqService,
    private readonly eventHandlerFactory: WebhookEventHandlerFactory,
    private readonly walletCreatedHandler: WalletCreatedEventHandler,
    private readonly walletUpdatedHandler: WalletUpdatedEventHandler,
  ) {
    super(rmqService);
  }

  /**
   * Register event handlers for wallet events
   */
  protected registerEventHandlers(): void {
    // Register event handlers with the factory
    this.eventHandlerFactory.registerHandler(
      Events.CREATE_WALLET,
      this.walletCreatedHandler,
    );
    this.eventHandlerFactory.registerHandler(
      Events.WALLET_UPDATED,
      this.walletUpdatedHandler,
    );

    // Subscribe to events
    this.subscribe(Events.CREATE_WALLET);
    this.subscribe(Events.WALLET_UPDATED);
  }

  /**
   * Get the handler for an event
   * @param eventName The event name (extracted from routing key)
   * @returns A function that handles the event
   */
  protected getEventHandler(eventName: string): (data: any) => Promise<void> {
    // Map the extracted event name to the full event key
    let fullEventKey: string;

    switch (eventName) {
      case 'generated':
        fullEventKey = Events.CREATE_WALLET;
        this.logger.debug(`Mapped event '${eventName}' to '${fullEventKey}'`);
        break;
      case 'updated':
        fullEventKey = Events.WALLET_UPDATED;
        this.logger.debug(`Mapped event '${eventName}' to '${fullEventKey}'`);
        break;
      default:
        this.logger.warn(`Unknown wallet event: ${eventName}`);
        return null;
    }

    const handler = this.eventHandlerFactory.getHandler(fullEventKey);
    if (handler) {
      return handler.handle.bind(handler);
    }

    this.logger.warn(`No handler found for mapped event: ${fullEventKey}`);
    return null;
  }
}
