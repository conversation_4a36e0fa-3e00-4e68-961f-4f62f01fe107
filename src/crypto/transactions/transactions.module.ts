import { Module } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { TransactionsController } from './transactions.controller';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxModule } from '@app/quidax';
import { PaymentCacheModule, RabbitmqModule } from '@crednet/utils';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionsWebhookConsumer } from './transaction.webhook.consumer';
import { TransactionPaymentConsumer } from './transaction.payment.consumer';
import { UserRepository } from '../repositories/users.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { WebhookModule } from '../../utils/webhook/webhook.module';
import {
  WithdrawalSuccessEventHandler,
  WithdrawalFailedEventHandler,
  PaymentProcessedEventHandler,
} from './transactions.event-handlers';
import { BullModule } from '@nestjs/bullmq';
import { Events } from '../../utils/queue';
import { TransactionPaymentCron } from './transaction.payment.cron';

@Module({
  imports: [
    QuidaxModule,
    RabbitmqModule,
    WebhookModule,
    BullModule.registerQueue({ name: Events.REQUERY_TRANSACTION }),
    PaymentCacheModule,
  ],
  controllers: [TransactionsController],
  providers: [
    TransactionsService,
    TransactionRepository,
    WalletRepository,
    TransactionsWebhookConsumer,
    TransactionPaymentConsumer,
    UserRepository,
    CurrencyRepository,
    WithdrawalSuccessEventHandler,
    WithdrawalFailedEventHandler,
    PaymentProcessedEventHandler,
    TransactionPaymentCron,
  ],
})
export class TransactionsModule {}
